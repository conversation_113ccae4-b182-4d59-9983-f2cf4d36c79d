import React, { useState, useMemo } from 'react'
import { ChevronDown, ChevronRight, Search, X } from 'lucide-react'
import { Input } from '@repo/shared/components/ui/input'
import { Button } from '@repo/shared/components/ui/button'
import { ScrollArea } from '@repo/shared/components/ui/scroll-area'
import { Badge } from '@repo/shared/components/ui/badge'
import { useDynamicValues, searchValueSources } from '../hooks/useDynamicValues'
import { ResolvedValueSource } from '../types'
import { cn } from '@repo/shared/lib/utils'

interface ValueSourcePickerProps {
  onSelect: (path: string) => void
  selectedPath?: string
  className?: string
  placeholder?: string
}

interface CategoryNodeProps {
  categoryName: string
  widgets: Record<string, {
    widgetName: string
    componentType: string
    sources: ResolvedValueSource[]
  }>
  selectedPath?: string
  onSelect: (path: string) => void
}

function CategoryNode({ categoryName, widgets, selectedPath, onSelect }: CategoryNodeProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  
  const categoryDisplayName = categoryName.charAt(0).toUpperCase() + categoryName.slice(1)
  
  return (
    <div className="space-y-1">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 w-full text-left p-2 hover:bg-accent rounded-md transition-colors"
      >
        {isExpanded ? (
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        ) : (
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
        )}
        <span className="font-medium text-sm">{categoryDisplayName}</span>
        <Badge variant="secondary" className="ml-auto text-xs">
          {Object.keys(widgets).length}
        </Badge>
      </button>
      
      {isExpanded && (
        <div className="ml-6 space-y-1">
          {Object.entries(widgets).map(([widgetId, widget]) => (
            <WidgetNode
              key={widgetId}
              widget={widget}
              selectedPath={selectedPath}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface WidgetNodeProps {
  widget: {
    widgetName: string
    componentType: string
    sources: ResolvedValueSource[]
  }
  selectedPath?: string
  onSelect: (path: string) => void
}

function WidgetNode({ widget, selectedPath, onSelect }: WidgetNodeProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  return (
    <div className="space-y-1">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 w-full text-left p-2 hover:bg-accent rounded-md transition-colors"
      >
        {isExpanded ? (
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        ) : (
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
        )}
        <span className="text-sm text-muted-foreground">{widget.widgetName}</span>
        <Badge variant="outline" className="ml-auto text-xs">
          {widget.sources.length}
        </Badge>
      </button>
      
      {isExpanded && (
        <div className="ml-6 space-y-1">
          {widget.sources.map((source) => (
            <ValueSourceItem
              key={source.path}
              source={source}
              isSelected={selectedPath === source.path}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface ValueSourceItemProps {
  source: ResolvedValueSource
  isSelected: boolean
  onSelect: (path: string) => void
}

function ValueSourceItem({ source, isSelected, onSelect }: ValueSourceItemProps) {
  return (
    <button
      onClick={() => onSelect(source.path)}
      className={cn(
        "flex items-center justify-between w-full text-left p-2 rounded-md transition-colors",
        isSelected 
          ? "bg-primary text-primary-foreground" 
          : "hover:bg-accent"
      )}
    >
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{source.label}</div>
        {source.description && (
          <div className="text-xs text-muted-foreground truncate">{source.description}</div>
        )}
      </div>
      <Badge variant={isSelected ? "secondary" : "outline"} className="ml-2 text-xs">
        {source.type}
      </Badge>
    </button>
  )
}

export function ValueSourcePicker({ 
  onSelect, 
  selectedPath, 
  className,
  placeholder = "Search value sources..." 
}: ValueSourcePickerProps) {
  const { availableSources, isReady } = useDynamicValues()
  const [searchTerm, setSearchTerm] = useState('')
  
  // Filter sources based on search term
  const filteredSources = useMemo(() => {
    if (!searchTerm.trim()) {
      return availableSources
    }
    
    const searchResults = searchValueSources(availableSources, searchTerm)
    
    // Rebuild catalog structure from search results
    const filtered: typeof availableSources = {}
    
    for (const result of searchResults) {
      if (!filtered[result.category]) {
        filtered[result.category] = {}
      }
      
      // Find the original widget data
      const originalWidget = availableSources[result.category]?.[result.path.split('/')[1]]
      if (originalWidget) {
        if (!filtered[result.category][result.path.split('/')[1]]) {
          filtered[result.category][result.path.split('/')[1]] = {
            ...originalWidget,
            sources: []
          }
        }
        
        // Add matching source
        const matchingSource = originalWidget.sources.find(s => s.path === result.path)
        if (matchingSource) {
          filtered[result.category][result.path.split('/')[1]].sources.push(matchingSource)
        }
      }
    }
    
    return filtered
  }, [availableSources, searchTerm])
  
  const clearSearch = () => setSearchTerm('')
  
  if (!isReady) {
    return (
      <div className={cn("p-4 text-center text-muted-foreground", className)}>
        Loading value sources...
      </div>
    )
  }
  
  const hasAnySources = Object.keys(availableSources).length > 0
  
  if (!hasAnySources) {
    return (
      <div className={cn("p-4 text-center text-muted-foreground", className)}>
        No value sources available. Add components that expose dynamic values to see them here.
      </div>
    )
  }
  
  return (
    <div className={cn("space-y-3", className)}>
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-9 pr-9"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
      
      {/* Results */}
      <ScrollArea className="h-[400px]">
        <div className="space-y-2">
          {Object.keys(filteredSources).length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              No value sources match your search.
            </div>
          ) : (
            Object.entries(filteredSources).map(([categoryName, widgets]) => (
              <CategoryNode
                key={categoryName}
                categoryName={categoryName}
                widgets={widgets}
                selectedPath={selectedPath}
                onSelect={onSelect}
              />
            ))
          )}
        </div>
      </ScrollArea>
      
      {/* Selected path display */}
      {selectedPath && (
        <div className="p-2 bg-muted rounded-md">
          <div className="text-xs text-muted-foreground">Selected:</div>
          <div className="text-sm font-mono break-all">{selectedPath}</div>
        </div>
      )}
    </div>
  )
}

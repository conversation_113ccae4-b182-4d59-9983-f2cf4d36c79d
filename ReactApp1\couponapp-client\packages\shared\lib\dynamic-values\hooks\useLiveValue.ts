import { useState, useEffect, useRef } from 'react'
import { useDynamicValuesContext } from '../provider'

/**
 * Hook to subscribe to a live value and get real-time updates
 * @param path - The value path to subscribe to
 * @param defaultValue - Default value to return if path is not set
 * @returns Current value of the path
 */
export function useLiveValue<T = any>(path: string, defaultValue?: T): T {
  const { getValue, subscribe } = useDynamicValuesContext()
  
  // Initialize with current value or default
  const [value, setValue] = useState<T>(() => {
    const currentValue = getValue(path)
    return currentValue !== undefined ? currentValue : defaultValue
  })
  
  // Track if component is mounted to avoid state updates after unmount
  const isMountedRef = useRef(true)
  
  useEffect(() => {
    // Subscribe to value changes
    const unsubscribe = subscribe(path, (newValue: T) => {
      if (isMountedRef.current) {
        setValue(newValue !== undefined ? newValue : defaultValue)
      }
    })
    
    // Update with current value in case it changed between render and effect
    const currentValue = getValue(path)
    if (currentValue !== undefined && isMountedRef.current) {
      setValue(currentValue)
    }
    
    return () => {
      unsubscribe()
    }
  }, [path, subscribe, getValue, defaultValue])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])
  
  return value
}

/**
 * Hook to subscribe to multiple live values at once
 * @param paths - Array of paths to subscribe to
 * @param defaultValues - Object with default values for each path
 * @returns Object with current values for each path
 */
export function useLiveValues<T extends Record<string, any>>(
  paths: string[],
  defaultValues?: Partial<T>
): Record<string, any> {
  const { getValue, subscribe } = useDynamicValuesContext()
  
  // Initialize with current values or defaults
  const [values, setValues] = useState<Record<string, any>>(() => {
    const initialValues: Record<string, any> = {}
    
    for (const path of paths) {
      const currentValue = getValue(path)
      const defaultValue = defaultValues?.[path]
      initialValues[path] = currentValue !== undefined ? currentValue : defaultValue
    }
    
    return initialValues
  })
  
  const isMountedRef = useRef(true)
  
  useEffect(() => {
    const unsubscribeFunctions: (() => void)[] = []
    
    // Subscribe to all paths
    for (const path of paths) {
      const unsubscribe = subscribe(path, (newValue: any) => {
        if (isMountedRef.current) {
          setValues(prev => ({
            ...prev,
            [path]: newValue !== undefined ? newValue : defaultValues?.[path]
          }))
        }
      })
      
      unsubscribeFunctions.push(unsubscribe)
      
      // Update with current value
      const currentValue = getValue(path)
      if (currentValue !== undefined && isMountedRef.current) {
        setValues(prev => ({
          ...prev,
          [path]: currentValue
        }))
      }
    }
    
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe())
    }
  }, [paths, subscribe, getValue, defaultValues])
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])
  
  return values
}

/**
 * Hook to publish a value (for data producers)
 * @param path - The path to publish to
 * @returns Function to update the value
 */
export function usePublishValue(path: string) {
  const { setValue } = useDynamicValuesContext()
  
  return (value: any) => {
    setValue(path, value)
  }
}

export type ValueUnit = {
  value: string;
  unit: string;
};

export type EffectPropertyType =
  | "opacity"
  | "scale"
  | "color"
  | "rotate"
  | "skewX"
  | "skewY"
  | "offsetX"
  | "offsetY"
  | "x"
  | "y";

export type EffectTriggerType = "hover" | "press" | "focus" | "appear";

export type TransformPropertyType =
  | "rotate"
  | "skewX"
  | "skewY"
  | "offsetX"
  | "offsetY"
  | "x"
  | "y";

/**
 * Represents a property of an animation effect
 * @property {EffectPropertyType} type - The type of effect property (e.g., opacity, scale)
 * @property {number | string} value - The target value for the animation
 * @property {number | string} [fromValue] - Optional starting value for the animation.
 *                                          When not provided, the animation starts from the element's current value.
 */
export interface EffectProperty {
  type: EffectPropertyType;
  value: number | string;
  fromValue?: number | string;
}

export interface Effect {
  id: string;
  trigger: EffectTriggerType;
  properties: EffectProperty[];
  duration?: number;
  ease?: string;
}

export interface EffectsSettings extends WidgetSettings {
  effects: Effect[];
}

export interface WidgetSettings extends Record<string, any> {}

/**
 * Represents dynamic text that can contain both static text and dynamic expressions
 * @property {string} expression - The text expression with embedded variables like "Score: {game/123456/score}"
 */
export interface DynamicText {
  expression: string;
}

export interface AccordionItem {
  title: string;
  content: string;
}

export interface AccordionWidgetSettings
  extends WidgetSettings,
    MarginSettings,
    PaddingSettings,
    BorderSettings,
    FontSettings {
  items: AccordionItem[];
  allowMultiple: boolean;
  backgroundColor: string;
  headerBgColor: string;
  headerTextColor: string;
}

export interface MarginSettings extends WidgetSettings {
  marginTop: { value: string; unit: string };
  marginRight: { value: string; unit: string };
  marginBottom: { value: string; unit: string };
  marginLeft: { value: string; unit: string };
}
export interface PaddingSettings extends WidgetSettings {
  paddingTop: { value: string; unit: string };
  paddingRight: { value: string; unit: string };
  paddingBottom: { value: string; unit: string };
  paddingLeft: { value: string; unit: string };
}

export interface ContentAlignmentSettings extends WidgetSettings {
  horizontalAlignment: "flex-start" | "center" | "flex-end";
  verticalAlignment: "flex-start" | "center" | "flex-end";
}

export interface BackgroundImageSettings extends WidgetSettings {
  backgroundImage: AssetUrl;
}
export interface FontSettings extends WidgetSettings {
  textColor: string;
  fontSize: string;
  fontWeight: string;
  fontFamily: string;
  fontStyle: string;
}

export interface BorderSettings extends WidgetSettings {
  border?: {
    width: string;
    style: string;
    color: string;
    radius: string;
  };
  // Legacy properties for backward compatibility
  borderStyle?: string;
  borderWidth?: string;
  borderColor?: string;
  borderRadius?: string;
}
export interface SizeSettings extends WidgetSettings {
  sizeMode: "fluid" | "fixed" | "fit-content";
  width: { value: string; unit: string };
  height: { value: string; unit: string };
  maxWidth: { value: string; unit: string };
  maxHeight: { value: string; unit: string };
}

export type AssetUrl = {
  assetId?: string;
  absoluteUrl?: string;
  isVisible?: boolean;
};

export type SoundAssetUrl = AssetUrl & {
  enabled: boolean;
  volume?: number; // Volume level from 0 to 1, default is 0.5 if not specified
};

export type AssetBackground = {
  asset?: AssetUrl;
  fill?: string;
};

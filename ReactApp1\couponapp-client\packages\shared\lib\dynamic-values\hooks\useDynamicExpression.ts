import { useMemo } from 'react';
import { useLiveValues } from './useLiveValue';
import { renderExpression, extractVariablePaths } from '../expression-parser';

/**
 * Hook to render dynamic text expressions with live value updates
 * @param expression - The expression string with variables like "Score: {game/123456/score}"
 * @param fallbackValue - Value to use when a variable is not found
 * @returns Rendered string with variables replaced by live values
 */
export function useDynamicExpression(expression: string, fallbackValue: string = ''): string {
  // Extract all variable paths from the expression
  const variablePaths = useMemo(() => {
    return extractVariablePaths(expression);
  }, [expression]);

  // Subscribe to all dynamic values used in the expression
  const dynamicValues = useLiveValues(variablePaths);

  // Render the expression with current dynamic values
  const renderedText = useMemo(() => {
    return renderExpression(
      expression,
      (path: string) => dynamicValues[path],
      fallbackValue
    );
  }, [expression, dynamicValues, fallbackValue]);

  return renderedText;
}

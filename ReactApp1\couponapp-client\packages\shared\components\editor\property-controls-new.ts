import React from 'react';


export enum ControlType {
    Text = 'text',
    Number = 'number',
    Boolean = 'boolean',
    Enum = 'enum',
    Color = 'color',
    Margin = 'margin',
    Padding = 'padding',
    Border = 'border',
    LeaderboardId = 'leaderboardId',
    GameConfigEditor = 'gameConfigEditor',
    GameSkinSelector = 'gameSkinSelector',
    IconPicker = 'iconPicker',
    Asset = 'asset',
    FlowNode = 'flowNode',
    DynamicText = 'dynamicText'
}

export interface PropertyControl {
    type: ControlType;
    title?: string;
    defaultValue?: any
    options?: {label: string, value: any}[]
    hidden?: (props: any) => boolean
}

export interface PropertyControls {
    [key: string]: PropertyControl;
}


export const PropertyControlsRegistry: Record<string, PropertyControls> = {};

export function addPropertyControlsNew(
    component: React.ComponentType<any>,
    controls: PropertyControls
) {
    const componentName =  component.displayName;
    console.log("Add property controls", componentName, controls);
    if (!componentName) {
        console.warn('Component must have a displayName or name to add property controls');
        return;
    }

    PropertyControlsRegistry[componentName] = {
        ...PropertyControlsRegistry[componentName],
        ...controls
    };
}

export function getPropertyControlsNew(componentName: string): PropertyControls | undefined {
    return PropertyControlsRegistry[componentName];
}

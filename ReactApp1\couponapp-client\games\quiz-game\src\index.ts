import MainGame from './game/GameMain'
import { ConfigKeyEditor } from './editor/ConfigKeyEditor'
import MainConfigEditor from './editor/MainConfigEditor'
import { PreviewScene } from './game/Scenes'
import { defaultGameConfig, ReactGameConfig } from './types/config'
import { GameScreenId } from './types/screen'
import { GameModule, PreviewScreenDefinition } from '@repo/shared/lib/game/game'
import { registerValueSources } from '@repo/shared/lib/dynamic-values/registry'

// Preview screen definitions for the game
const previewScreens: PreviewScreenDefinition[] = [
    {
        screenId: 'start' as GameScreenId,
        displayName: 'Start Screen',
        visibleCheck: (config) => config.gameStartHandler?.enableStartScreen === true,
    },
    {
        screenId: 'main' as GameScreenId,
        displayName: 'Main Game Screen',
    },
    {
        screenId: 'try-again' as GameScreenId,
        displayName: 'Try again',
        visibleCheck: (config) => config.gameRewardsHandler?.rewardsEnabled === true || config.gameEndHandler?.useLives === true,
    },
    {
        screenId: 'claim-reward' as GameScreenId,
        displayName: 'Claim Reward',
        visibleCheck: (config) => config.gameRewardsHandler?.rewardsEnabled === true,
    },
    {
        screenId: 'out-of-lives' as GameScreenId,
        displayName: 'Game over',
        visibleCheck: () => true, // Always show game over screen since it's used for all end scenarios
    },
]

const widgetSlots = [
    {
        widgetId: 'outOfLives',
        displayName: 'Game Over',
    },
    {
        widgetId: 'tryAgain',
        displayName: 'Try Again',
    },
    {
        widgetId: 'gameOver',
        displayName: 'Game Over',
    },
    {
        widgetId: 'rewardScreen',
        displayName: 'Reward Screen',
    },
]

const QuizGame: GameModule = {
    id: 'quiz-game',
    name: 'Quiz Game',
    runtimeComponent: MainGame,
    editorComponent: MainConfigEditor,
    configKeyEditor: ConfigKeyEditor,
    defaultConfig: defaultGameConfig,
    previewScene: PreviewScene,
    previewScreens: previewScreens,
    configType: ReactGameConfig,
    widgetSlots: widgetSlots
}

export default QuizGame

export { MainGame, MainConfigEditor }


registerValueSources('quiz-game', [
  {
    path: "game/{widgetId}/score",
    type: "number",
    label: "Player Score",
    description: "Current player's game score"
  },
  {
    path: "game/{widgetId}/currentQuestion",
    type: "number",
    label: "Current question",
    description: "The current question that is being displayed in game"
  }
])
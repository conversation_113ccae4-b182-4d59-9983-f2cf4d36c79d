import { useEffect, useState } from 'react'
import { Helmet } from 'react-helmet-async'
import { useParams } from 'react-router-dom'
import { FontsLoader } from '@repo/shared/components/FontsLoader'
import { getErrorMessage } from '@repo/shared/lib/utils'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { CampaignDataProvider } from '@repo/shared/lib/hooks/useCampaignStore'
import { CampaignViewer } from '@/pages/play/_components/campaignViewer'
import { sendEvent } from '@/lib/events'
import { CampaignService } from '@/lib/services/campaignService'
import { DynamicValuesProvider } from '@repo/shared/lib/dynamic-values/provider'

export default function CampaignPlay() {
    return <CampaignComponent />
}
function CampaignComponent() {
    const { id } = useParams()
    const [sceneError, setSceneError] = useState<string | null>(null)

    
        
    const { data, error, isLoading } = useQuery({
        queryKey: ['campaign/' + id],
        queryFn: () => CampaignService.getCampaign(id),
        retry: false,
    })

    useEffect(() => {
        if (data?.campaign) {
            const url = new URL(window.location.href)
            const sceneId = url.searchParams.get('s')

            if (sceneId) {
                const scene = data.campaign.config.scenes.find((s) => s.id === sceneId)
                if (!scene) {
                    setSceneError(`Invalid scene ID: ${sceneId}`)
                }
            }
        }
    }, [data])

    if (error) {
        return (
            <div className="flex items-center flex-grow justify-center h-[100vh] w-full">
                <div>We are sorry...</div>
                <div>{getErrorMessage(error)}</div>
            </div>
        )
    }

    if (isLoading) return <div>Please wait, loading</div>

    if (sceneError) {
        return (
            <div className="flex items-center flex-grow justify-center h-[100vh] w-full">
                <div>Scene Error</div>
                <div>{sceneError}</div>
            </div>
        )
    }

    return (
        <div >
            <FontsLoader scenes={data?.campaign?.config?.scenes} />
            <Integrations data={data} />
            <CampaignDataProvider campaignData={data}>
                <DynamicValuesProvider>
                <CampaignViewer campaignData={data} />
                </DynamicValuesProvider>
            </CampaignDataProvider>
        </div>
    )
}

const Integrations = ({ data }) => {
    const tiktokScript = `
         !function (w, d, t) {
         w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(
         var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var r="https://analytics.tiktok.com/i18n/pixel/events.js",o=n&&n.partner;ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=r,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script")
         ;n.type="text/javascript",n.async=!0,n.src=r+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
         ttq.load('${data.tiktokAdsPixelId}');
         ttq.page();
         }(window, document, 'ttq');
     `

    const googleAnalyticsScript = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '${data.googleAnalyticsMeasurementId}');
     `

    const metaPixelScript = `
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '${data.metaPixelId}');
        fbq('track', 'PageView');
     `

    const plausibleScript = `
       window.plausible =
        window.plausible ||
        function () {
          (window.plausible.q = window.plausible.q || []).push(arguments);
        };
     `

    return (
        <Helmet>
            <title>My Title</title>

            <script defer data-domain="play.beakbyte.com" event-campaign={data.campaign?.id + 'WTF'} src="https://plausible-dev.beakbyte.com/js/script.pageview-props.tagged-events.local.js"></script>

            <script>{plausibleScript}</script>

            {data.tiktokAdsPixelId && <script>{tiktokScript}</script>}

            {data.googleAnalyticsMeasurementId && <script async src={`https://www.googletagmanager.com/gtag/js?id=${data.googleAnalyticsMeasurementId}`}></script>}
            {data.googleAnalyticsMeasurementId && <script>{googleAnalyticsScript}</script>}

            {data.metaPixelId && <script>{metaPixelScript}</script>}
        </Helmet>
    )
}



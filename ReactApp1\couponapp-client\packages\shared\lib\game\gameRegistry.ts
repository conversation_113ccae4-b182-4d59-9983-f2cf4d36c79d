import { GameModule } from './game'

export const gameModules = new Map<string, GameModule>()

export function registerGameModule(module: GameModule) {
    console.log('Registering game: ', module.id)
    gameModules.set(module.id, module)
}

export function getGameModule(id: string) {
    return gameModules.get(id)
}



export const gameModulesList = [
    '2048',
    'flappy-bird',
    'game-boilerplate-react',
    'slots-game',
    'scratch-to-win',
    'memory',
    'lootbox',
    'spin-to-win',
    'quiz-game',
    'dont-touch-spikes',
]


export async function loadGameModules() {
    for (const gameId of gameModulesList) {
         loadGameModule(gameId)
    }
}

export async function loadGameModule(gameId: string) {
    try {
        if (gameId === '2048') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/2048/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'flappy-bird') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/flappy-bird/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'game-boilerplate-react') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/game-boilerplate-react/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'slots-game') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/slots-game/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'scratch-to-win') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/scratch-to-win/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'memory') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/memory/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'lootbox') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/lootbox/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'spin-to-win') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/spin-to-win/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'quiz-game') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/quiz-game/src`)
            registerGameModule(module.default)
            return module.default
        }
        if (gameId === 'dont-touch-spikes') {
            //@ts-ignore TODO UNIGNORE THAT
            const module = await import(`@games/dont-touch-spikes/src`)
            registerGameModule(module.default)
            return module.default
        }
        console.log('Game not found: ', gameId)
        return null
    } catch (error) {
        console.error(`Failed to load game module: ${gameId}`, error)
        console.error(error)
        throw error
    }
}

export interface DynamicValueSource {
  path: string                        // "game/{widgetId}/stats/score"
  type: 'number' | 'string' | 'boolean' | 'object'
  label: string                       // "Player Score"
  description?: string                // "Current player's game score"
}

export interface ResolvedValueSource {
  path: string                        // "game/123456/stats/score" (resolved)
  type: 'number' | 'string' | 'boolean' | 'object'
  label: string
  description?: string
  widgetId: string                    // "123456"
  widgetName?: string                 // "Quiz Game #1"
  componentType: string               // "quiz-game", "timer-widget", etc.
}

export interface ValueSourceCatalog {
  [category: string]: {               // "game", "timer", "form", "custom"
    [widgetId: string]: {
      widgetName: string
      componentType: string
      sources: ResolvedValueSource[]
    }
  }
}

export interface ValueSubscription {
  path: string
  callback: (value: any) => void
  unsubscribe: () => void
}

export interface DynamicValuesContextValue {
  catalog: ValueSourceCatalog
  getValue: (path: string) => any
  setValue: (path: string, value: any) => void
  subscribe: (path: string, callback: (value: any) => void) => () => void
  isReady: boolean
}

import { useEffect } from 'react'
import { useDynamicValuesContext } from '../provider'
import { ValueSourceCatalog } from '../types'



export function useSetDynamicValue(key: string, value: any) {
    const { setValue } = useDynamicValuesContext()
    
    useEffect(() => {
      setValue(key, value)
    }, [value])
}


export function useDynamicValues() {
  const { catalog, getValue, setValue, isReady } = useDynamicValuesContext()
  
  return {
    /**
     * Hierarchical catalog of all available value sources
     */
    availableSources: catalog,
    
    /**
     * Get current value for a path
     * @param path - The value path (e.g., "game/123456/score")
     * @returns Current value or undefined if not set
     */
    getValue,
    
    /**
     * Set value for a path (typically used by data producers)
     * @param path - The value path
     * @param value - The value to set
     */
    setValue,
    
    /**
     * Whether the dynamic values system is ready
     */
    isReady
  }
}

/**
 * Helper function to get all paths from the catalog
 * @param catalog - The value source catalog
 * @returns Array of all available paths
 */
export function getAllAvailablePaths(catalog: ValueSourceCatalog): string[] {
  const paths: string[] = []
  
  for (const category of Object.values(catalog)) {
    for (const widget of Object.values(category)) {
      for (const source of widget.sources) {
        paths.push(source.path)
      }
    }
  }
  
  return paths
}

/**
 * Helper function to search for value sources by label or path
 * @param catalog - The value source catalog
 * @param searchTerm - The search term
 * @returns Array of matching sources with their full context
 */
export function searchValueSources(catalog: ValueSourceCatalog, searchTerm: string) {
  const results: Array<{
    path: string
    label: string
    description?: string
    category: string
    widgetName: string
    componentType: string
  }> = []
  
  const lowerSearchTerm = searchTerm.toLowerCase()
  
  for (const [categoryName, category] of Object.entries(catalog)) {
    for (const [widgetId, widget] of Object.entries(category)) {
      for (const source of widget.sources) {
        const matchesLabel = source.label.toLowerCase().includes(lowerSearchTerm)
        const matchesPath = source.path.toLowerCase().includes(lowerSearchTerm)
        const matchesDescription = source.description?.toLowerCase().includes(lowerSearchTerm)
        
        if (matchesLabel || matchesPath || matchesDescription) {
          results.push({
            path: source.path,
            label: source.label,
            description: source.description,
            category: categoryName,
            widgetName: widget.widgetName,
            componentType: widget.componentType
          })
        }
      }
    }
  }
  
  return results
}
